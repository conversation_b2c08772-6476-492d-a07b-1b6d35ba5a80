# 函数单调性

## 📖 核心概念

### 定义
设函数 f(x) 在区间 I 上有定义：
- **单调递增**：对于区间 I 内任意 x₁ < x₂，都有 f(x₁) ≤ f(x₂)
- **单调递减**：对于区间 I 内任意 x₁ < x₂，都有 f(x₁) ≥ f(x₂)
- **严格单调**：上述不等号为严格不等号

### 几何意义
- 单调递增：函数图像从左到右上升
- 单调递减：函数图像从左到右下降

## 🔧 判断方法

### 1. 定义法
直接利用单调性定义，通过作差或作商来判断

### 2. 导数法（最常用）
- 若 f'(x) > 0 在区间 I 上恒成立，则 f(x) 在 I 上单调递增
- 若 f'(x) < 0 在区间 I 上恒成立，则 f(x) 在 I 上单调递减
- 若 f'(x) = 0，则为驻点，需进一步分析

### 3. 复合函数单调性
- 同增异减：内外函数同为增（减）函数，则复合函数为增函数
- 内外函数一增一减，则复合函数为减函数

## 💡 常见考点

1. **求函数的单调区间**
   - 求导数 f'(x)
   - 解不等式 f'(x) > 0 和 f'(x) < 0
   - 注意定义域的限制

2. **利用单调性解不等式**
   - 如果 f(x) 单调，则 f(a) > f(b) ⟺ a > b（递增时）

3. **单调性的应用**
   - 比较函数值大小
   - 求参数范围
   - 证明不等式

## ⚠️ 常见错误

1. **忘记考虑定义域**：单调区间必须在定义域内
2. **驻点处理错误**：f'(x) = 0 的点不一定是单调性改变点
3. **区间表示错误**：单调区间通常用开区间表示

## 🔗 相关知识点

- [[高等数学/导数的几何意义]]
- [[高等数学/函数的性质]]
- [[高等数学/不等式的证明]]
- [[高等数学/参数的取值范围]]

---

# 📊 知识点掌握度分析

```dataviewjs
// 获取当前知识点名称
const currentKnowledgePoint = dv.current().file.name;

// 查询所有链接到当前知识点的错题
const relatedQuestions = dv.pages('"02_WrongQuestions"')
    .where(p => p.tags && p.tags.includes("错题"))
    .where(p => p.file.outlinks.some(link => link.path.includes(currentKnowledgePoint)));

if (relatedQuestions.length === 0) {
    dv.paragraph("📝 暂无相关错题");
} else {
    // 统计各状态题目数量
    const totalQuestions = relatedQuestions.length;
    const masteredQuestions = relatedQuestions.where(p => p.status === '已掌握').length;
    const reviewingQuestions = relatedQuestions.where(p => p.status === '复习中').length;
    const newQuestions = relatedQuestions.where(p => p.status === '新题').length;
    
    // 计算掌握度百分比
    const masteryPercentage = totalQuestions > 0 ? Math.round((masteredQuestions / totalQuestions) * 100) : 0;
    
    // 计算平均掌握等级
    const avgMasteryLevel = totalQuestions > 0 ? 
        (relatedQuestions.map(p => p.mastery_level || 0).reduce((a, b) => a + b, 0) / totalQuestions).toFixed(1) : 0;
    
    // 生成进度条
    const progressBarLength = 20;
    const filledBars = Math.round((masteryPercentage / 100) * progressBarLength);
    const emptyBars = progressBarLength - filledBars;
    const progressBar = "█".repeat(filledBars) + "░".repeat(emptyBars);
    
    // 显示掌握度概览
    dv.header(3, "🎯 掌握度概览");
    dv.paragraph(`
**总题数：** ${totalQuestions} 道
**掌握度：** ${masteryPercentage}% ${progressBar}
**平均等级：** ${avgMasteryLevel}/5.0 ⭐

**状态分布：**
- ✅ 已掌握：${masteredQuestions} 道 (${Math.round(masteredQuestions/totalQuestions*100)}%)
- 🔄 复习中：${reviewingQuestions} 道 (${Math.round(reviewingQuestions/totalQuestions*100)}%)
- 🆕 新题：${newQuestions} 道 (${Math.round(newQuestions/totalQuestions*100)}%)
    `);
    
    // 掌握度等级分布
    const levelDistribution = [0, 1, 2, 3, 4, 5].map(level => {
        const count = relatedQuestions.where(p => (p.mastery_level || 0) === level).length;
        return { level, count, percentage: Math.round(count/totalQuestions*100) };
    });
    
    dv.header(4, "📈 掌握等级分布");
    const levelTable = levelDistribution.map(item => [
        `${item.level} ⭐`,
        item.count,
        `${item.percentage}%`,
        "█".repeat(Math.ceil(item.percentage / 5)) || "░"
    ]);
    
    dv.table(["等级", "题数", "占比", "分布图"], levelTable);
}
```

## 📚 相关错题详情

```dataviewjs
// 获取当前知识点名称
const currentKnowledgePoint = dv.current().file.name;

// 查询所有链接到当前知识点的错题
const relatedQuestions = dv.pages('"02_WrongQuestions"')
    .where(p => p.tags && p.tags.includes("错题"))
    .where(p => p.file.outlinks.some(link => link.path.includes(currentKnowledgePoint)))
    .sort(p => p.date, 'desc');

if (relatedQuestions.length > 0) {
    // 按状态分组显示
    const statusGroups = {
        '新题': relatedQuestions.where(p => p.status === '新题'),
        '复习中': relatedQuestions.where(p => p.status === '复习中'),
        '已掌握': relatedQuestions.where(p => p.status === '已掌握')
    };
    
    Object.entries(statusGroups).forEach(([status, questions]) => {
        if (questions.length > 0) {
            dv.header(4, `${getStatusIcon(status)} ${status} (${questions.length} 道)`);
            
            const tableData = questions.map(p => {
                const masteryStars = "⭐".repeat(p.mastery_level || 0) + "☆".repeat(5 - (p.mastery_level || 0));
                const nextReview = p.next_review_date || "未设置";
                const isOverdue = p.next_review_date && p.next_review_date < moment().format('YYYY-MM-DD');
                const overdueFlag = isOverdue ? "⚠️" : "";
                
                return [
                    `[[${p.file.name}|${p.file.name.replace('.md', '')}]]`,
                    p.subject || "未分类",
                    p.difficulty || "未设置",
                    masteryStars,
                    nextReview + " " + overdueFlag,
                    p.date
                ];
            });
            
            dv.table(
                ["题目", "学科", "难度", "掌握等级", "下次复习", "记录日期"],
                tableData
            );
        }
    });
} else {
    dv.paragraph("📝 暂无相关错题，开始添加错题来建立知识点关联吧！");
}

function getStatusIcon(status) {
    const icons = {
        '新题': '🆕',
        '复习中': '🔄',
        '已掌握': '✅'
    };
    return icons[status] || '📝';
}
```

## 🎯 复习建议

```dataviewjs
// 获取当前知识点名称
const currentKnowledgePoint = dv.current().file.name;

// 查询相关错题
const relatedQuestions = dv.pages('"02_WrongQuestions"')
    .where(p => p.tags && p.tags.includes("错题"))
    .where(p => p.file.outlinks.some(link => link.path.includes(currentKnowledgePoint)));

if (relatedQuestions.length > 0) {
    const totalQuestions = relatedQuestions.length;
    const masteredQuestions = relatedQuestions.where(p => p.status === '已掌握').length;
    const masteryPercentage = Math.round((masteredQuestions / totalQuestions) * 100);
    
    // 今日需要复习的题目
    const today = moment().format('YYYY-MM-DD');
    const todayReview = relatedQuestions.where(p => 
        p.next_review_date && p.next_review_date <= today && p.status !== '已掌握'
    );
    
    // 生成复习建议
    let suggestions = [];
    
    if (masteryPercentage < 30) {
        suggestions.push("🔥 **重点关注**：该知识点掌握度较低，建议加强基础概念学习");
    } else if (masteryPercentage < 70) {
        suggestions.push("📈 **持续提升**：掌握度中等，继续巩固练习");
    } else {
        suggestions.push("🎉 **掌握良好**：该知识点掌握度较高，保持复习节奏");
    }
    
    if (todayReview.length > 0) {
        suggestions.push(`⏰ **今日任务**：有 ${todayReview.length} 道相关错题需要复习`);
    }
    
    // 分析错误原因
    const reasonStats = {};
    relatedQuestions.forEach(p => {
        if (p.tags) {
            p.tags.forEach(tag => {
                if (tag.startsWith('原因/')) {
                    const reason = tag.replace('原因/', '');
                    reasonStats[reason] = (reasonStats[reason] || 0) + 1;
                }
            });
        }
    });
    
    if (Object.keys(reasonStats).length > 0) {
        const topReason = Object.entries(reasonStats).sort(([,a], [,b]) => b - a)[0];
        suggestions.push(`🎯 **主要问题**：最常见错误原因是"${topReason[0]}"，出现 ${topReason[1]} 次`);
    }
    
    if (suggestions.length > 0) {
        dv.header(4, "💡 智能建议");
        suggestions.forEach(suggestion => {
            dv.paragraph(suggestion);
        });
    }
}
```

---

**💡 使用说明：** 这个知识点笔记会自动分析所有链接到它的错题，提供掌握度统计、进度可视化和智能复习建议。当你在错题中使用 [[高等数学/函数单调性]] 链接时，该知识点的掌握度数据会自动更新。
