# 🏷️ 标签汇总与分析

## 📊 所有标签统计

```dataviewjs
// 获取所有文件的标签
const allFiles = dv.pages();
const tagStats = {};

// 统计每个标签的使用次数
allFiles.forEach(file => {
    if (file.tags) {
        file.tags.forEach(tag => {
            tagStats[tag] = (tagStats[tag] || 0) + 1;
        });
    }
});

// 按使用次数排序
const sortedTags = Object.entries(tagStats)
    .sort(([,a], [,b]) => b - a);

if (sortedTags.length > 0) {
    dv.header(3, `📈 标签使用统计 (共 ${sortedTags.length} 个标签)`);
    
    const tableData = sortedTags.map(([tag, count]) => {
        const percentage = ((count / allFiles.length) * 100).toFixed(1);
        const bar = "█".repeat(Math.ceil(count / Math.max(...Object.values(tagStats)) * 20));
        
        return [
            `#${tag}`,
            count,
            `${percentage}%`,
            bar
        ];
    });
    
    dv.table(
        ["标签", "使用次数", "使用率", "频率图"],
        tableData
    );
} else {
    dv.paragraph("📝 暂无标签数据");
}
```

## 🎯 错题相关标签

```dataviewjs
// 专门统计错题相关的标签
const wrongQuestions = dv.pages('"02_WrongQuestions"')
    .where(p => p.tags && p.tags.includes("错题"));

const categoryStats = {
    '错误原因': {},
    '知识点': {},
    '学科': {},
    '其他': {}
};

wrongQuestions.forEach(file => {
    if (file.tags) {
        file.tags.forEach(tag => {
            if (tag.startsWith('原因/')) {
                const reason = tag.replace('原因/', '');
                categoryStats['错误原因'][reason] = (categoryStats['错误原因'][reason] || 0) + 1;
            } else if (tag.startsWith('知识点/')) {
                const knowledge = tag.replace('知识点/', '');
                categoryStats['知识点'][knowledge] = (categoryStats['知识点'][knowledge] || 0) + 1;
            } else if (!['错题'].includes(tag)) {
                // 假设其他非特殊标签为学科标签
                if (tag.length <= 4 && !tag.includes('/')) {
                    categoryStats['学科'][tag] = (categoryStats['学科'][tag] || 0) + 1;
                } else {
                    categoryStats['其他'][tag] = (categoryStats['其他'][tag] || 0) + 1;
                }
            }
        });
    }
});

// 显示各类别统计
Object.entries(categoryStats).forEach(([category, stats]) => {
    if (Object.keys(stats).length > 0) {
        dv.header(4, `${getCategoryIcon(category)} ${category}`);
        
        const sortedStats = Object.entries(stats)
            .sort(([,a], [,b]) => b - a);
        
        const tableData = sortedStats.map(([item, count]) => [
            item,
            count,
            `${((count / wrongQuestions.length) * 100).toFixed(1)}%`
        ]);
        
        dv.table(
            ["名称", "出现次数", "占比"],
            tableData
        );
    }
});

function getCategoryIcon(category) {
    const icons = {
        '错误原因': '🎯',
        '知识点': '📚',
        '学科': '📖',
        '其他': '🏷️'
    };
    return icons[category] || '📝';
}
```

## 🔍 标签使用建议

```dataviewjs
// 分析标签使用情况并给出建议
const wrongQuestions = dv.pages('"02_WrongQuestions"')
    .where(p => p.tags && p.tags.includes("错题"));

const reasonTags = [];
const knowledgeTags = [];

wrongQuestions.forEach(file => {
    if (file.tags) {
        file.tags.forEach(tag => {
            if (tag.startsWith('原因/')) {
                reasonTags.push(tag.replace('原因/', ''));
            } else if (tag.startsWith('知识点/')) {
                knowledgeTags.push(tag.replace('知识点/', ''));
            }
        });
    }
});

const reasonStats = {};
const knowledgeStats = {};

reasonTags.forEach(tag => {
    reasonStats[tag] = (reasonStats[tag] || 0) + 1;
});

knowledgeTags.forEach(tag => {
    knowledgeStats[tag] = (knowledgeStats[tag] || 0) + 1;
});

dv.header(3, "💡 标签优化建议");

// 检查是否有未分类的错误原因
const filesWithoutReason = wrongQuestions.where(p => 
    !p.tags.some(tag => tag.startsWith('原因/'))
).length;

if (filesWithoutReason > 0) {
    dv.paragraph(`⚠️ **发现 ${filesWithoutReason} 道错题缺少错误原因标签**，建议补充以便更好地分析错误模式。`);
}

// 检查是否有未分类的知识点
const filesWithoutKnowledge = wrongQuestions.where(p => 
    !p.tags.some(tag => tag.startsWith('知识点/'))
).length;

if (filesWithoutKnowledge > 0) {
    dv.paragraph(`📚 **发现 ${filesWithoutKnowledge} 道错题缺少知识点标签**，建议添加知识点链接以便知识点掌握度分析。`);
}

// 推荐常用标签
if (Object.keys(reasonStats).length > 0) {
    const topReasons = Object.entries(reasonStats)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3);
    
    dv.paragraph("🎯 **最常见错误原因**：" + topReasons.map(([reason, count]) => `${reason}(${count}次)`).join("、"));
}
```

## 📋 标签管理工具

### 🏷️ 推荐标签体系

**错误原因标签**：
- `原因/概念不清` - 基础概念理解不透彻
- `原因/计算失误` - 计算过程出错
- `原因/审题错误` - 题目理解有误
- `原因/思路阻塞` - 解题思路不清晰
- `原因/知识遗忘` - 相关知识点遗忘
- `原因/方法错误` - 解题方法选择错误

**知识点标签**：
- `知识点/函数` - 函数相关
- `知识点/导数` - 导数相关
- `知识点/积分` - 积分相关
- `知识点/极限` - 极限相关

**学科标签**：
- `高数` - 高等数学
- `线代` - 线性代数
- `概率` - 概率论与数理统计
- `政治` - 政治
- `英语` - 英语

### 🔧 标签清理建议

```dataview
TABLE tags as "当前标签"
FROM "02_WrongQuestions"
WHERE tags
WHERE !contains(string(tags), "错题")
SORT file.name
```

---

**💡 使用提示**：
1. 定期查看此页面了解标签使用情况
2. 保持标签体系的一致性
3. 及时为新错题添加适当的标签
4. 定期清理和规范化标签命名
