# 矩阵的秩

## 📖 核心概念

### 定义
矩阵的秩是指矩阵中线性无关的行（或列）的最大个数。

### 几何意义
- 矩阵的秩表示矩阵所代表的线性变换的像空间的维数
- n×n矩阵可逆当且仅当其秩为n

## 🔧 计算方法

### 1. 行阶梯形法
通过初等行变换将矩阵化为行阶梯形，非零行的个数即为矩阵的秩

### 2. 定义法
找出矩阵中线性无关的行向量（或列向量）的最大个数

## 💡 重要性质

1. **秩的不等式**：rank(A+B) ≤ rank(A) + rank(B)
2. **乘积的秩**：rank(AB) ≤ min{rank(A), rank(B)}
3. **转置矩阵**：rank(A) = rank(A^T)

## 🔗 相关知识点

- [[线性代数/线性相关性]]
- [[线性代数/矩阵的初等变换]]
- [[线性代数/线性方程组]]

---

# 📊 知识点掌握度分析

```dataviewjs
// 获取当前知识点名称
const currentKnowledgePoint = dv.current().file.name;

// 查询所有链接到当前知识点的错题
const relatedQuestions = dv.pages('"02_WrongQuestions"')
    .where(p => p.tags && p.tags.includes("错题"))
    .where(p => p.file.outlinks.some(link => link.path.includes(currentKnowledgePoint)));

if (relatedQuestions.length === 0) {
    dv.paragraph("📝 暂无相关错题");
} else {
    // 统计各状态题目数量
    const totalQuestions = relatedQuestions.length;
    const masteredQuestions = relatedQuestions.where(p => p.status === '已掌握').length;
    const reviewingQuestions = relatedQuestions.where(p => p.status === '复习中').length;
    const newQuestions = relatedQuestions.where(p => p.status === '新题').length;
    
    // 计算掌握度百分比
    const masteryPercentage = totalQuestions > 0 ? Math.round((masteredQuestions / totalQuestions) * 100) : 0;
    
    // 计算平均掌握等级
    const avgMasteryLevel = totalQuestions > 0 ? 
        (relatedQuestions.map(p => p.mastery_level || 0).reduce((a, b) => a + b, 0) / totalQuestions).toFixed(1) : 0;
    
    // 生成进度条
    const progressBarLength = 20;
    const filledBars = Math.round((masteryPercentage / 100) * progressBarLength);
    const emptyBars = progressBarLength - filledBars;
    const progressBar = "█".repeat(filledBars) + "░".repeat(emptyBars);
    
    // 显示掌握度概览
    dv.header(3, "🎯 掌握度概览");
    dv.paragraph(`
**总题数：** ${totalQuestions} 道
**掌握度：** ${masteryPercentage}% ${progressBar}
**平均等级：** ${avgMasteryLevel}/5.0 ⭐

**状态分布：**
- ✅ 已掌握：${masteredQuestions} 道 (${Math.round(masteredQuestions/totalQuestions*100)}%)
- 🔄 复习中：${reviewingQuestions} 道 (${Math.round(reviewingQuestions/totalQuestions*100)}%)
- 🆕 新题：${newQuestions} 道 (${Math.round(newQuestions/totalQuestions*100)}%)
    `);
}
```

---

**💡 使用说明：** 当你在错题中使用 [[线性代数/矩阵的秩]] 链接时，该知识点的掌握度数据会自动更新。
