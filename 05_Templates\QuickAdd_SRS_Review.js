/**
 * QuickAdd 脚本：SRS 错题复习功能
 * 用于处理错题复习按钮点击和更新元数据
 */

module.exports = async (params) => {
    const { quickAddApi: QuickAdd } = params;
    
    // 获取当前活动文件
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
        new Notice("❌ 请先打开一个错题文件");
        return;
    }
    
    // 检查是否为错题文件
    const fileCache = app.metadataCache.getFileCache(activeFile);
    const frontmatter = fileCache?.frontmatter;
    
    if (!frontmatter || !frontmatter.tags?.includes("错题")) {
        new Notice("❌ 当前文件不是错题文件");
        return;
    }
    
    // 显示复习选项
    const reviewOptions = [
        "✅ 简单 (掌握等级+1，延长复习间隔)",
        "🤔 模糊 (等级不变，稍微延长间隔)", 
        "❌ 忘记 (等级-1，明天重新复习)",
        "🚫 取消复习"
    ];
    
    const reviewResults = ["easy", "medium", "hard", "cancel"];
    
    const choice = await QuickAdd.suggester(reviewOptions, reviewResults);
    
    if (!choice || choice === "cancel") {
        new Notice("已取消复习");
        return;
    }
    
    try {
        // 计算新的复习数据
        const reviewData = calculateNextReview(frontmatter, choice);
        
        // 更新文件元数据
        await updateFileFrontmatter(activeFile, reviewData);
        
        // 显示成功消息
        const statusEmoji = {
            "easy": "🎉",
            "medium": "👍", 
            "hard": "💪"
        };
        
        new Notice(`${statusEmoji[choice]} 复习完成！下次复习：${reviewData.next_review_date}`);
        
    } catch (error) {
        console.error("复习更新失败:", error);
        new Notice("❌ 复习更新失败，请检查文件格式");
    }
};

/**
 * 计算下次复习时间和掌握等级
 */
function calculateNextReview(frontmatter, reviewResult) {
    const today = moment().format('YYYY-MM-DD');
    const currentLevel = frontmatter.mastery_level || 0;
    const reviewHistory = frontmatter.review_history || [];
    
    let newLevel = currentLevel;
    let intervalDays = 1;
    let newStatus = frontmatter.status || '新题';
    
    // 根据复习结果调整等级和间隔
    switch (reviewResult) {
        case 'easy':
            newLevel = Math.min(currentLevel + 1, 5);
            intervalDays = getIntervalForLevel(newLevel);
            newStatus = newLevel >= 5 ? '已掌握' : '复习中';
            break;
            
        case 'medium':
            // 等级不变，稍微延长间隔
            intervalDays = Math.max(1, Math.floor(getIntervalForLevel(currentLevel) * 0.6));
            newStatus = '复习中';
            break;
            
        case 'hard':
            newLevel = Math.max(currentLevel - 1, 0);
            intervalDays = 1; // 明天重新复习
            newStatus = '复习中';
            break;
    }
    
    // 计算下次复习日期
    const nextReviewDate = moment().add(intervalDays, 'days').format('YYYY-MM-DD');
    
    // 更新复习历史
    const newReviewHistory = [...reviewHistory, {
        date: today,
        result: reviewResult,
        level_before: currentLevel,
        level_after: newLevel,
        interval_days: intervalDays
    }];
    
    return {
        status: newStatus,
        mastery_level: newLevel,
        next_review_date: nextReviewDate,
        last_reviewed: today,
        review_history: newReviewHistory
    };
}

/**
 * 根据掌握等级获取复习间隔（天数）
 */
function getIntervalForLevel(level) {
    const intervals = [1, 3, 7, 15, 30, 90]; // 对应等级 0-5 的间隔天数
    return intervals[Math.min(level, intervals.length - 1)];
}

/**
 * 更新文件的 frontmatter
 */
async function updateFileFrontmatter(file, newData) {
    const content = await app.vault.read(file);
    const lines = content.split('\n');
    
    // 找到 frontmatter 边界
    let frontmatterStart = -1;
    let frontmatterEnd = -1;
    
    for (let i = 0; i < lines.length; i++) {
        if (lines[i].trim() === '---') {
            if (frontmatterStart === -1) {
                frontmatterStart = i;
            } else {
                frontmatterEnd = i;
                break;
            }
        }
    }
    
    if (frontmatterStart === -1 || frontmatterEnd === -1) {
        throw new Error("无法找到有效的 frontmatter");
    }
    
    // 解析现有 frontmatter
    const frontmatterLines = lines.slice(frontmatterStart + 1, frontmatterEnd);
    let updatedFrontmatter = frontmatterLines.join('\n');
    
    // 更新各个字段
    Object.entries(newData).forEach(([key, value]) => {
        const regex = new RegExp(`^${key}:.*$`, 'm');
        const newLine = `${key}: ${formatYamlValue(value)}`;
        
        if (regex.test(updatedFrontmatter)) {
            updatedFrontmatter = updatedFrontmatter.replace(regex, newLine);
        } else {
            // 如果字段不存在，添加到 frontmatter 末尾
            updatedFrontmatter += `\n${newLine}`;
        }
    });
    
    // 重新组装文件内容
    const newContent = [
        '---',
        updatedFrontmatter,
        '---',
        ...lines.slice(frontmatterEnd + 1)
    ].join('\n');
    
    // 写入文件
    await app.vault.modify(file, newContent);
}

/**
 * 格式化 YAML 值
 */
function formatYamlValue(value) {
    if (typeof value === 'string') {
        return `"${value}"`;
    } else if (Array.isArray(value)) {
        return JSON.stringify(value);
    } else {
        return value;
    }
}
