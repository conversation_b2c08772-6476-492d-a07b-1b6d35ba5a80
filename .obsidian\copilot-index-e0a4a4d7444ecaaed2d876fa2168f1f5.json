{"internalDocumentIDStore": {"internalIdToId": ["761e0dcb13a5d2cf89958c051e787d92", "7faffe75d4c19ed413a506117e9d2232", "503ec10ef09a8e656f29a6e99cc8fd04", "31549058158759d98c5d2dfd4d57653c", "7b32fb086d6985177a8adc6813d138f5", "ee13dc4b33c292035f788c26dbe3e221", "823edb27083100ca90ab6a3832277e7b"]}, "index": {"indexes": {"id": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "c": {"3": {"w": "31549058158759d98c5d2dfd4d57653c", "s": "31549058158759d98c5d2dfd4d57653c", "c": {}, "d": [], "e": true}, "5": {"w": "503ec10ef09a8e656f29a6e99cc8fd04", "s": "503ec10ef09a8e656f29a6e99cc8fd04", "c": {}, "d": [], "e": true}, "7": {"w": "7", "s": "7", "c": {"6": {"w": "761e0dcb13a5d2cf89958c051e787d92", "s": "61e0dcb13a5d2cf89958c051e787d92", "c": {}, "d": [], "e": true}, "f": {"w": "7faffe75d4c19ed413a506117e9d2232", "s": "faffe75d4c19ed413a506117e9d2232", "c": {}, "d": [], "e": true}, "b": {"w": "7b32fb086d6985177a8adc6813d138f5", "s": "b32fb086d6985177a8adc6813d138f5", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "8": {"w": "823edb27083100ca90ab6a3832277e7b", "s": "823edb27083100ca90ab6a3832277e7b", "c": {}, "d": [], "e": true}, "e": {"w": "ee13dc4b33c292035f788c26dbe3e221", "s": "ee13dc4b33c292035f788c26dbe3e221", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "isArray": false}, "title": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "c": {"1": {"w": "1", "s": "1", "c": {}, "d": [], "e": true}, "2": {"w": "20250712_113241", "s": "20250712_113241", "c": {}, "d": [], "e": true}, "t": {"w": "t", "s": "t", "c": {"e": {"w": "test", "s": "est", "c": {}, "d": [], "e": true}, "_": {"w": "t_wrongquestion", "s": "_wrongquestion", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "h": {"w": "hello", "s": "hello", "c": {}, "d": [], "e": true}, "a": {"w": "aiprompt", "s": "aiprompt", "c": {}, "d": [], "e": true}, "u": {"w": "untitled", "s": "untitled", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "isArray": false}, "path": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "c": {"0": {"w": "0", "s": "0", "c": {"2": {"w": "02_wrongquestions", "s": "2_wrongquestions", "c": {}, "d": [], "e": true}, "3": {"w": "03_knowledgebase", "s": "3_knowledgebase", "c": {}, "d": [], "e": true}, "5": {"w": "05_templates", "s": "5_templates", "c": {}, "d": [], "e": true}, "7": {"w": "07_aich<PERSON>", "s": "7_aich<PERSON>", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "1": {"w": "1", "s": "1", "c": {}, "d": [], "e": true}, "2": {"w": "20250712_113241", "s": "20250712_113241", "c": {}, "d": [], "e": true}, "m": {"w": "md", "s": "md", "c": {}, "d": [], "e": true}, "t": {"w": "t", "s": "t", "c": {"e": {"w": "test", "s": "est", "c": {}, "d": [], "e": true}, "_": {"w": "t_wrongquestion", "s": "_wrongquestion", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "h": {"w": "hello", "s": "hello", "c": {}, "d": [], "e": true}, "a": {"w": "aiprompt", "s": "aiprompt", "c": {}, "d": [], "e": true}, "u": {"w": "untitled", "s": "untitled", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "isArray": false}, "content": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "c": {"0": {"w": "0", "s": "0", "c": {"2": {"w": "02", "s": "2", "c": {"_": {"w": "02_wrongquestions", "s": "_wrongquestions", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "7": {"w": "07", "s": "7", "c": {}, "d": [], "e": true}, "x": {"w": "0x", "s": "x", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "1": {"w": "1", "s": "1", "c": {"0": {"w": "10", "s": "0", "c": {}, "d": [], "e": true}, "1": {"w": "11", "s": "1", "c": {}, "d": [], "e": true}, "2": {"w": "12", "s": "2", "c": {}, "d": [], "e": true}, "5": {"w": "15", "s": "5", "c": {}, "d": [], "e": true}, "7": {"w": "1752291161477", "s": "752291161477", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "2": {"w": "2", "s": "2", "c": {"0": {"w": "2025", "s": "025", "c": {"0": {"w": "20250712_113241", "s": "0712_113241", "c": {}, "d": [], "e": true}, "-": {"w": "2025-07-11", "s": "-07-11", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "1": {"w": "21", "s": "1", "c": {}, "d": [], "e": true}, "3": {"w": "23", "s": "3", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "3": {"w": "3", "s": "3", "c": {"2": {"w": "32", "s": "2", "c": {}, "d": [], "e": true}, "4": {"w": "34", "s": "4", "c": {}, "d": [], "e": true}, "5": {"w": "35", "s": "5", "c": {}, "d": [], "e": true}, "6": {"w": "36", "s": "6", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "4": {"w": "4", "s": "4", "c": {"1": {"w": "41", "s": "1", "c": {}, "d": [], "e": true}, "3": {"w": "43", "s": "3", "c": {}, "d": [], "e": true}, "4": {"w": "44", "s": "4", "c": {}, "d": [], "e": true}, "5": {"w": "45", "s": "5", "c": {}, "d": [], "e": true}, "6": {"w": "46", "s": "6", "c": {}, "d": [], "e": true}, "8": {"w": "48", "s": "8", "c": {}, "d": [], "e": true}, "9": {"w": "49", "s": "9", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "5": {"w": "5", "s": "5", "c": {"4": {"w": "54", "s": "4", "c": {}, "d": [], "e": true}, "5": {"w": "55", "s": "5", "c": {}, "d": [], "e": true}, "6": {"w": "56", "s": "6", "c": {}, "d": [], "e": true}, "7": {"w": "57", "s": "7", "c": {}, "d": [], "e": true}, "9": {"w": "59", "s": "9", "c": {}, "d": [], "e": true}, "-": {"w": "5-pro", "s": "-pro", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "6": {"w": "6", "s": "6", "c": {}, "d": [], "e": true}, "7": {"w": "7", "s": "7", "c": {}, "d": [], "e": true}, "8": {"w": "8", "s": "8", "c": {}, "d": [], "e": true}, "n": {"w": "n", "s": "n", "c": {"o": {"w": "note", "s": "ote", "c": {"-": {"w": "note-taking", "s": "-taking", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "u": {"w": "null", "s": "ull", "c": {}, "d": [], "e": true}, "a": {"w": "name", "s": "ame", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "t": {"w": "t", "s": "t", "c": {"2": {"w": "t2", "s": "2", "c": {}, "d": [], "e": true}, "i": {"w": "ti", "s": "i", "c": {"t": {"w": "title", "s": "tle", "c": {}, "d": [], "e": true}, "m": {"w": "time", "s": "me", "c": {"s": {"w": "timestamp", "s": "stamp", "c": {}, "d": [], "e": true}}, "d": [], "e": true}}, "d": [], "e": false}, "a": {"w": "ta", "s": "a", "c": {"b": {"w": "table", "s": "ble", "c": {}, "d": [], "e": true}, "g": {"w": "tag", "s": "g", "c": {"s": {"w": "tags", "s": "s", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "n": {"w": "tan", "s": "n", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "o": {"w": "to", "s": "o", "c": {"d": {"w": "today", "s": "day", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "e": {"w": "test", "s": "est", "c": {}, "d": [], "e": true}, "_": {"w": "t_wrongquestion", "s": "_wrongquestion", "c": {}, "d": [], "e": true}, "h": {"w": "this", "s": "his", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "m": {"w": "m", "s": "m", "c": {"e": {"w": "metadata", "s": "etadata", "c": {}, "d": [], "e": true}, "o": {"w": "mod", "s": "od", "c": {"i": {"w": "modified", "s": "ified", "c": {}, "d": [], "e": true}, "e": {"w": "modelkey", "s": "el<PERSON>", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "a": {"w": "map", "s": "ap", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "c": {"w": "c", "s": "c", "c": {"r": {"w": "created", "s": "reated", "c": {}, "d": [], "e": true}, "o": {"w": "co", "s": "o", "c": {"n": {"w": "con", "s": "n", "c": {"t": {"w": "cont", "s": "t", "c": {"e": {"w": "content", "s": "ent", "c": {}, "d": [], "e": true}, "a": {"w": "contains", "s": "ains", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "s": {"w": "const", "s": "st", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "u": {"w": "count", "s": "unt", "c": {}, "d": [], "e": true}, "p": {"w": "copilot", "s": "pilot", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "h": {"w": "chat", "s": "hat", "c": {}, "d": [], "e": true}, "a": {"w": "can", "s": "an", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "b": {"w": "b", "s": "b", "c": {"l": {"w": "block", "s": "lock", "c": {}, "d": [], "e": true}, "y": {"w": "by", "s": "y", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "d": {"w": "d", "s": "d", "c": {"a": {"w": "da", "s": "a", "c": {"t": {"w": "dat", "s": "t", "c": {"a": {"w": "dataview", "s": "aview", "c": {"j": {"w": "dataviewjs", "s": "js", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "e": {"w": "date", "s": "e", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "y": {"w": "days", "s": "ys", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "v": {"w": "dv", "s": "v", "c": {}, "d": [], "e": true}, "i": {"w": "difficulty", "s": "ifficulty", "c": {}, "d": [], "e": true}, "u": {"w": "dur", "s": "ur", "c": {}, "d": [], "e": true}, "e": {"w": "desc", "s": "esc", "c": {}, "d": [], "e": true}, "t": {"w": "dt", "s": "t", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "p": {"w": "p", "s": "p", "c": {"a": {"w": "pages", "s": "ages", "c": {}, "d": [], "e": true}, "i": {"w": "pi", "s": "i", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "a": {"w": "a", "s": "a", "c": {"n": {"w": "and", "s": "nd", "c": {}, "d": [], "e": true}, "s": {"w": "as", "s": "s", "c": {"s": {"w": "assistant", "s": "sistant", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "i": {"w": "ai", "s": "i", "c": {"p": {"w": "aiprompt", "s": "prompt", "c": {}, "d": [], "e": true}}, "d": [], "e": true}}, "d": [], "e": true}, "-": {"w": "-", "s": "-", "c": {"-": {"w": "--", "s": "-", "c": {"-": {"w": "---", "s": "-", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "n": {"w": "-n", "s": "n", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "s": {"w": "s", "s": "s", "c": {"u": {"w": "subject", "s": "ubject", "c": {"c": {"w": "subjectcounts", "s": "counts", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "o": {"w": "so", "s": "o", "c": {"u": {"w": "source", "s": "urce", "c": {}, "d": [], "e": true}, "r": {"w": "sort", "s": "rt", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "t": {"w": "status", "s": "tatus", "c": {}, "d": [], "e": true}, "i": {"w": "sin", "s": "in", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "g": {"w": "g", "s": "g", "c": {"r": {"w": "group", "s": "roup", "c": {"b": {"w": "groupby", "s": "by", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "e": {"w": "gemini-2", "s": "emini-2", "c": {}, "d": [], "e": true}, "o": {"w": "google", "s": "oogle", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "k": {"w": "key", "s": "key", "c": {}, "d": [], "e": true}, "r": {"w": "r", "s": "r", "c": {"o": {"w": "rows", "s": "ows", "c": {}, "d": [], "e": true}, "i": {"w": "right", "s": "ight", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "l": {"w": "l", "s": "l", "c": {"e": {"w": "le", "s": "e", "c": {"n": {"w": "length", "s": "ngth", "c": {}, "d": [], "e": true}, "f": {"w": "left", "s": "ft", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "i": {"w": "li", "s": "i", "c": {"n": {"w": "link", "s": "nk", "c": {}, "d": [], "e": true}, "s": {"w": "list", "s": "st", "c": {}, "d": [], "e": true}, "m": {"w": "lim", "s": "m", "c": {"x": {"w": "limx", "s": "x", "c": {}, "d": [], "e": true}, "_": {"w": "lim_", "s": "_", "c": {}, "d": [], "e": true}}, "d": [], "e": false}}, "d": [], "e": false}}, "d": [], "e": false}, "f": {"w": "f", "s": "f", "c": {"i": {"w": "file", "s": "ile", "c": {"l": {"w": "filelink", "s": "link", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "r": {"w": "fr", "s": "r", "c": {"o": {"w": "fro", "s": "o", "c": {"m": {"w": "from", "s": "m", "c": {}, "d": [], "e": true}, "n": {"w": "frontmatter", "s": "ntmatter", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "a": {"w": "frac", "s": "ac", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "l": {"w": "flatten", "s": "latten", "c": {}, "d": [], "e": true}, "o": {"w": "for", "s": "or", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "i": {"w": "i", "s": "i", "c": {"t": {"w": "item", "s": "tem", "c": {}, "d": [], "e": true}, "d": {"w": "id", "s": "d", "c": {}, "d": [], "e": true}, "'": {"w": "i'm", "s": "'m", "c": {}, "d": [], "e": true}, "n": {"w": "infty", "s": "nfty", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "w": {"w": "w", "s": "w", "c": {"i": {"w": "without", "s": "ithout", "c": {}, "d": [], "e": true}, "h": {"w": "where", "s": "here", "c": {}, "d": [], "e": true}, "r": {"w": "wrong_count", "s": "rong_count", "c": {}, "d": [], "e": true}, "q": {"w": "wq-", "s": "q-", "c": {"2": {"w": "wq-20250711-234553", "s": "20250711-234553", "c": {}, "d": [], "e": true}}, "d": [], "e": true}}, "d": [], "e": false}, "h": {"w": "h", "s": "h", "c": {"e": {"w": "hel", "s": "el", "c": {"l": {"w": "hello", "s": "lo", "c": {}, "d": [], "e": true}, "p": {"w": "help", "s": "p", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "o": {"w": "how", "s": "ow", "c": {}, "d": [], "e": true}, "h": {"w": "hhmmss", "s": "hmmss", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "e": {"w": "epoch", "s": "epoch", "c": {}, "d": [], "e": true}, "u": {"w": "u", "s": "u", "c": {"s": {"w": "user", "s": "ser", "c": {}, "d": [], "e": true}, "n": {"w": "untitled", "s": "ntitled", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "o": {"w": "obsidian", "s": "obsidian", "c": {}, "d": [], "e": true}, "y": {"w": "y", "s": "y", "c": {"o": {"w": "you", "s": "ou", "c": {"r": {"w": "your", "s": "r", "c": {}, "d": [], "e": true}}, "d": [], "e": true}, "y": {"w": "yyyy", "s": "yyy", "c": {"m": {"w": "yyyymmdd", "s": "mmdd", "c": {}, "d": [], "e": true}, "-": {"w": "yyyy-mm-dd", "s": "-mm-dd", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "a": {"w": "yaml", "s": "aml", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "x": {"w": "x3", "s": "x3", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "isArray": false}, "embeddingModel": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "c": {"t": {"w": "text-embedding-004", "s": "text-embedding-004", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "isArray": false}, "created_at": {"type": "AVL", "node": {"root": {"k": 0, "v": [], "l": null, "r": null, "h": 0}}, "isArray": false}, "ctime": {"type": "AVL", "node": {"root": {"k": 0, "v": [], "l": null, "r": null, "h": 0}}, "isArray": false}, "mtime": {"type": "AVL", "node": {"root": {"k": 0, "v": [], "l": null, "r": null, "h": 0}}, "isArray": false}, "tags": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "c": {}, "d": [], "e": false}, "isArray": true}, "extension": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "c": {"m": {"w": "md", "s": "md", "c": {}, "d": [], "e": true}}, "d": [], "e": false}, "isArray": false}}, "vectorIndexes": {"embedding": {"size": 768, "vectors": {}}}, "searchableProperties": ["id", "title", "path", "content", "embedding", "embeddingModel", "created_at", "ctime", "mtime", "tags", "extension"], "searchablePropertiesWithTypes": {"id": "string", "title": "string", "path": "string", "content": "string", "embedding": "vector[768]", "embeddingModel": "string", "created_at": "number", "ctime": "number", "mtime": "number", "tags": "string[]", "extension": "string"}, "frequencies": {"id": {}, "title": {}, "path": {}, "content": {}, "embeddingModel": {}, "tags": {}, "extension": {}}, "tokenOccurrences": {"id": {"761e0dcb13a5d2cf89958c051e787d92": 0, "7faffe75d4c19ed413a506117e9d2232": 0, "503ec10ef09a8e656f29a6e99cc8fd04": 0, "31549058158759d98c5d2dfd4d57653c": 0, "7b32fb086d6985177a8adc6813d138f5": 0, "ee13dc4b33c292035f788c26dbe3e221": 0, "823edb27083100ca90ab6a3832277e7b": 0}, "title": {"1": 0, "test": 0, "hello": 0, "20250712_113241": 0, "aiprompt": 0, "t_wrongquestion": 0, "untitled": 0}, "path": {"1": 0, "md": 0, "test": 0, "07_aichats": 0, "hello": 0, "20250712_113241": 0, "aiprompt": 0, "05_templates": 0, "t_wrongquestion": 0, "03_knowledgebase": 0, "untitled": 0, "02_wrongquestions": 0}, "content": {"0": 0, "1": 0, "2": 0, "3": 0, "5": 0, "6": 0, "7": 0, "8": 0, "10": 0, "11": 0, "12": 0, "15": 0, "21": 0, "23": 0, "32": 0, "34": 0, "35": 0, "36": 0, "41": 0, "43": 0, "44": 0, "45": 0, "46": 0, "48": 0, "49": 0, "54": 0, "55": 0, "56": 0, "57": 0, "59": 0, "2025": 0, "note": 0, "title": 0, "metadata": 0, "created": 0, "07": 0, "02": 0, "modified": 0, "block": 0, "content": 0, "dataviewjs": 0, "const": 0, "pages": 0, "dv": 0, "and": 0, "-": 0, "subjectcounts": 0, "groupby": 0, "p": 0, "subject": 0, "map": 0, "g": 0, "key": 0, "count": 0, "rows": 0, "length": 0, "link": 0, "filelink": 0, "table": 0, "item": 0, "dataview": 0, "without": 0, "id": 0, "file": 0, "as": 0, "difficulty": 0, "source": 0, "from": 0, "where": 0, "status": 0, "date": 0, "today": 0, "dur": 0, "days": 0, "sort": 0, "desc": 0, "--": 0, "list": 0, "flatten": 0, "tags": 0, "tag": 0, "contains": 0, "group": 0, "by": 0, "test": 0, "wrong_count": 0, "02_wrongquestions": 0, "hello": 0, "20250712_113241": 0, "epoch": 0, "1752291161477": 0, "modelkey": 0, "gemini-2": 0, "5-pro": 0, "google": 0, "null": 0, "---": 0, "ai": 0, "chat": 0, "user": 0, "timestamp": 0, "i'm": 0, "obsidian": 0, "copilot": 0, "your": 0, "assistant": 0, "for": 0, "note-taking": 0, "how": 0, "can": 0, "i": 0, "help": 0, "you": 0, "aiprompt": 0, "limx": 0, "x3": 0, "0x": 0, "sin": 0, "t2": 0, "dt": 0, "t_wrongquestion": 0, "wq-": 0, "yyyymmdd": 0, "time": 0, "hhmmss": 0, "yyyy-mm-dd": 0, "yaml": 0, "frontmatter": 0, "a": 0, "b": 0, "untitled": 0, "name": 0, "this": 0, "wq-20250711-234553": 0, "2025-07-11": 0, "lim_": 0, "n": 0, "to": 0, "infty": 0, "left": 0, "tan": 0, "frac": 0, "pi": 0, "-n": 0, "right": 0}, "embeddingModel": {"text-embedding-004": 0}, "tags": {}, "extension": {"md": 0}}, "avgFieldLength": {"tags": null}, "fieldLengths": {"id": {}, "title": {}, "path": {}, "content": {}, "embeddingModel": {}, "tags": {}, "extension": {}}}, "docs": {"docs": {}, "count": 0}, "sorting": {"language": "english", "sortableProperties": ["id", "title", "path", "content", "embeddingModel", "created_at", "ctime", "mtime", "extension"], "sortablePropertiesWithTypes": {"id": "string", "title": "string", "path": "string", "content": "string", "embeddingModel": "string", "created_at": "number", "ctime": "number", "mtime": "number", "extension": "string"}, "sorts": {"id": {"docs": {"4": 0}, "orderedDocs": [[4, "31549058158759d98c5d2dfd4d57653c"]], "type": "string"}, "title": {"docs": {"7": 0}, "orderedDocs": [[7, "1"]], "type": "string"}, "path": {"docs": {"7": 0}, "orderedDocs": [[7, "02_WrongQuestions/1.md"]], "type": "string"}, "content": {"docs": {"7": 0}, "orderedDocs": [[7, "\n\nNOTE TITLE: [[1]]\n\nMETADATA:{\"id\":\"***********-234553\",\"subject\":\"高数\",\"source\":\"[[来源]]\",\"date\":\"2025-07-11\",\"difficulty\":\"★☆☆☆☆\",\"status\":\"待复习\",\"wrong_count\":5,\"tags\":[\"错题\",\"原因/概念不清\",\"原因/计算失误\",\"原因/审题错误\",\"原因/思路阻塞\",\"原因/知识遗忘\",\"知识点/\"],\"created\":\"2025/07/11 23:45:48\",\"modified\":\"2025/07/12 10:57:59\"}\n\nNOTE BLOCK CONTENT:\n\n---\nid: ***********-234553\nsubject: 高数\nsource: \"[[来源]]\"\ndate: 2025-07-11\ndifficulty: ★☆☆☆☆\nstatus: 待复习\nwrong_count: 5\ntags:\n  - 错题\n  - 原因/概念不清\n  - 原因/计算失误\n  - 原因/审题错误\n  - 原因/思路阻塞\n  - 原因/知识遗忘\n  - 知识点/\n---\n\n## 题目原文\n\n\n## 题目原文\n$\\lim_{n \\to \\infty} \\left[ \\tan\\left(\\frac{\\pi}{6} + 2^{-n}\\right) \\right]^n =$\n\n\n## 我的错误答案\n## 正确答案与解析\n## 错误原因分析 (反思)\n> 这是整个流程中最重要的一步！\n\n* **我为什么会犯这个错误？**\n    * * **根本原因是什么？是哪个概念没理解透彻？**\n    * * **下次遇到类似问题，我应该如何思考？**\n    * ## 知识点链接与归纳 (织网)\n* 这道题主要考察了 [[知识点A]]和 [[知识点B]]\n* 通过这道题，我深化了对 `[[某个概念]]` 的理解。"]], "type": "string"}, "embeddingModel": {"docs": {"1": 0}, "orderedDocs": [[1, "text-embedding-004"]], "type": "string"}, "created_at": {"docs": {"1": 0}, "orderedDocs": [[1, 1752291243116]], "type": "number"}, "ctime": {"docs": {"5": 0}, "orderedDocs": [[5, 1752248099676]], "type": "number"}, "mtime": {"docs": {"1": 0}, "orderedDocs": [[1, 1752248923878]], "type": "number"}, "extension": {"docs": {"1": 0}, "orderedDocs": [[1, "md"]], "type": "string"}}, "enabled": true, "isSorted": true}, "language": "english", "schema": {"id": "string", "title": "string", "path": "string", "content": "string", "embedding": "vector[768]", "embeddingModel": "string", "created_at": "number", "ctime": "number", "mtime": "number", "tags": "string[]", "extension": "string"}}