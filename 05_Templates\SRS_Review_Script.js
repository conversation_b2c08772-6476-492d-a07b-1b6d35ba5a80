/**
 * SRS 错题复习系统脚本
 * 用于处理复习按钮点击事件和更新错题元数据
 */

module.exports = async (params) => {
    const { quickAddApi: QuickAdd, variables } = params;
    
    // 获取当前文件
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
        new Notice("请先打开一个错题文件");
        return;
    }
    
    // 读取文件内容
    const fileContent = await app.vault.read(activeFile);
    const frontmatter = app.metadataCache.getFileCache(activeFile)?.frontmatter;
    
    if (!frontmatter || !frontmatter.tags?.includes("错题")) {
        new Notice("当前文件不是错题文件");
        return;
    }
    
    // 询问复习结果
    const reviewResult = await QuickAdd.quickAddApi.suggester(
        ["简单 ✅ (掌握等级+1，延长复习间隔)", "模糊 🤔 (等级不变，稍微延长)", "忘记 ❌ (等级-1，明天重新复习)"],
        ["easy", "medium", "hard"]
    );
    
    if (!reviewResult) return;
    
    // 计算新的复习数据
    const reviewData = calculateNextReview(frontmatter, reviewResult);
    
    // 更新文件元数据
    await updateFileFrontmatter(activeFile, fileContent, reviewData);
    
    new Notice(`复习完成！下次复习时间：${reviewData.next_review_date}`);
};

/**
 * 计算下次复习时间和掌握等级
 */
function calculateNextReview(frontmatter, reviewResult) {
    const today = moment().format('YYYY-MM-DD');
    const currentLevel = frontmatter.mastery_level || 0;
    const reviewHistory = frontmatter.review_history || [];
    
    let newLevel = currentLevel;
    let intervalDays = 1;
    let newStatus = frontmatter.status || '新题';
    
    // 根据复习结果调整等级和间隔
    switch (reviewResult) {
        case 'easy':
            newLevel = Math.min(currentLevel + 1, 5);
            intervalDays = getIntervalForLevel(newLevel);
            newStatus = newLevel >= 5 ? '已掌握' : '复习中';
            break;
            
        case 'medium':
            // 等级不变，稍微延长间隔
            intervalDays = Math.max(1, Math.floor(getIntervalForLevel(currentLevel) * 0.6));
            newStatus = '复习中';
            break;
            
        case 'hard':
            newLevel = Math.max(currentLevel - 1, 0);
            intervalDays = 1; // 明天重新复习
            newStatus = '复习中';
            break;
    }
    
    // 计算下次复习日期
    const nextReviewDate = moment().add(intervalDays, 'days').format('YYYY-MM-DD');
    
    // 更新复习历史
    const newReviewHistory = [...reviewHistory, {
        date: today,
        result: reviewResult,
        level_before: currentLevel,
        level_after: newLevel
    }];
    
    return {
        status: newStatus,
        mastery_level: newLevel,
        next_review_date: nextReviewDate,
        last_reviewed: today,
        review_history: newReviewHistory
    };
}

/**
 * 根据掌握等级获取复习间隔（天数）
 */
function getIntervalForLevel(level) {
    const intervals = [1, 3, 7, 15, 30, 90]; // 对应等级 0-5 的间隔天数
    return intervals[Math.min(level, intervals.length - 1)];
}

/**
 * 更新文件的 frontmatter
 */
async function updateFileFrontmatter(file, content, newData) {
    const lines = content.split('\n');
    let frontmatterEnd = -1;
    let frontmatterStart = -1;
    
    // 找到 frontmatter 的边界
    for (let i = 0; i < lines.length; i++) {
        if (lines[i].trim() === '---') {
            if (frontmatterStart === -1) {
                frontmatterStart = i;
            } else {
                frontmatterEnd = i;
                break;
            }
        }
    }
    
    if (frontmatterStart === -1 || frontmatterEnd === -1) {
        new Notice("无法找到文件的 frontmatter");
        return;
    }
    
    // 解析现有的 frontmatter
    const frontmatterLines = lines.slice(frontmatterStart + 1, frontmatterEnd);
    const frontmatterText = frontmatterLines.join('\n');
    
    // 更新 frontmatter 数据
    let updatedFrontmatter = frontmatterText;
    
    // 更新各个字段
    Object.entries(newData).forEach(([key, value]) => {
        const regex = new RegExp(`^${key}:.*$`, 'm');
        const newLine = `${key}: ${formatYamlValue(value)}`;
        
        if (regex.test(updatedFrontmatter)) {
            updatedFrontmatter = updatedFrontmatter.replace(regex, newLine);
        } else {
            // 如果字段不存在，添加到 frontmatter 末尾
            updatedFrontmatter += `\n${newLine}`;
        }
    });
    
    // 重新组装文件内容
    const newContent = [
        '---',
        updatedFrontmatter,
        '---',
        ...lines.slice(frontmatterEnd + 1)
    ].join('\n');
    
    // 写入文件
    await app.vault.modify(file, newContent);
}

/**
 * 格式化 YAML 值
 */
function formatYamlValue(value) {
    if (typeof value === 'string') {
        return `"${value}"`;
    } else if (Array.isArray(value)) {
        return JSON.stringify(value);
    } else {
        return value;
    }
}
