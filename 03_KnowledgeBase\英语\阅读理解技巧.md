# 阅读理解技巧

## 📖 核心策略

### 阅读步骤
1. **快速浏览**：了解文章大意和结构
2. **精读题目**：理解题目要求和关键词
3. **定位信息**：在文章中找到相关段落
4. **仔细分析**：分析选项与原文的关系

## 🔧 解题技巧

### 1. 主旨大意题
- 关注首段和尾段
- 注意转折词和总结性语言
- 避免过于具体或过于宽泛的选项

### 2. 细节理解题
- 精确定位关键信息
- 注意同义替换
- 警惕偷换概念

### 3. 推理判断题
- 基于文章内容进行合理推断
- 避免过度推理
- 注意作者的态度和倾向

### 4. 词汇猜测题
- 利用上下文语境
- 注意词根词缀
- 考虑词性和语法功能

## 💡 常见陷阱

1. **张冠李戴**：选项内容正确但不是题目所问
2. **偷换概念**：选项与原文表述相似但含义不同
3. **过度推理**：超出文章内容范围的推断
4. **绝对化表述**：含有always、never等绝对词的选项

## 🔗 相关知识点

- [[英语/词汇理解]]
- [[英语/语法分析]]
- [[英语/逻辑推理]]

---

# 📊 知识点掌握度分析

```dataviewjs
// 获取当前知识点名称
const currentKnowledgePoint = dv.current().file.name;

// 查询所有链接到当前知识点的错题
const relatedQuestions = dv.pages('"02_WrongQuestions"')
    .where(p => p.tags && p.tags.includes("错题"))
    .where(p => p.file.outlinks.some(link => link.path.includes(currentKnowledgePoint)));

if (relatedQuestions.length === 0) {
    dv.paragraph("📝 暂无相关错题");
} else {
    // 统计各状态题目数量
    const totalQuestions = relatedQuestions.length;
    const masteredQuestions = relatedQuestions.where(p => p.status === '已掌握').length;
    const reviewingQuestions = relatedQuestions.where(p => p.status === '复习中').length;
    const newQuestions = relatedQuestions.where(p => p.status === '新题').length;
    
    // 计算掌握度百分比
    const masteryPercentage = totalQuestions > 0 ? Math.round((masteredQuestions / totalQuestions) * 100) : 0;
    
    // 计算平均掌握等级
    const avgMasteryLevel = totalQuestions > 0 ? 
        (relatedQuestions.map(p => p.mastery_level || 0).reduce((a, b) => a + b, 0) / totalQuestions).toFixed(1) : 0;
    
    // 生成进度条
    const progressBarLength = 20;
    const filledBars = Math.round((masteryPercentage / 100) * progressBarLength);
    const emptyBars = progressBarLength - filledBars;
    const progressBar = "█".repeat(filledBars) + "░".repeat(emptyBars);
    
    // 显示掌握度概览
    dv.header(3, "🎯 掌握度概览");
    dv.paragraph(`
**总题数：** ${totalQuestions} 道
**掌握度：** ${masteryPercentage}% ${progressBar}
**平均等级：** ${avgMasteryLevel}/5.0 ⭐

**状态分布：**
- ✅ 已掌握：${masteredQuestions} 道 (${Math.round(masteredQuestions/totalQuestions*100)}%)
- 🔄 复习中：${reviewingQuestions} 道 (${Math.round(reviewingQuestions/totalQuestions*100)}%)
- 🆕 新题：${newQuestions} 道 (${Math.round(newQuestions/totalQuestions*100)}%)
    `);
}
```

---

**💡 使用说明：** 当你在错题中使用 [[英语/阅读理解技巧]] 链接时，该知识点的掌握度数据会自动更新。
