# 马克思主义基本原理

## 📖 核心概念

### 马克思主义的组成部分
1. **马克思主义哲学**
2. **马克思主义政治经济学**
3. **科学社会主义**

### 基本特征
- 科学性与革命性的统一
- 理论性与实践性的统一
- 阶级性与人民性的统一

## 🔧 重要理论

### 1. 唯物辩证法
- 对立统一规律（根本规律）
- 质量互变规律
- 否定之否定规律

### 2. 历史唯物主义
- 社会存在决定社会意识
- 生产力决定生产关系
- 经济基础决定上层建筑

### 3. 剩余价值理论
- 劳动力商品的特殊性
- 剩余价值的生产过程
- 资本主义剥削的本质

## 💡 常见考点

1. **马克思主义的基本特征**
2. **唯物辩证法的基本规律**
3. **认识论的基本问题**
4. **历史唯物主义的基本观点**

## 🔗 相关知识点

- [[政治/唯物辩证法]]
- [[政治/历史唯物主义]]
- [[政治/政治经济学]]

---

# 📊 知识点掌握度分析

```dataviewjs
// 获取当前知识点名称
const currentKnowledgePoint = dv.current().file.name;

// 查询所有链接到当前知识点的错题
const relatedQuestions = dv.pages('"02_WrongQuestions"')
    .where(p => p.tags && p.tags.includes("错题"))
    .where(p => p.file.outlinks.some(link => link.path.includes(currentKnowledgePoint)));

if (relatedQuestions.length === 0) {
    dv.paragraph("📝 暂无相关错题");
} else {
    // 统计各状态题目数量
    const totalQuestions = relatedQuestions.length;
    const masteredQuestions = relatedQuestions.where(p => p.status === '已掌握').length;
    const reviewingQuestions = relatedQuestions.where(p => p.status === '复习中').length;
    const newQuestions = relatedQuestions.where(p => p.status === '新题').length;
    
    // 计算掌握度百分比
    const masteryPercentage = totalQuestions > 0 ? Math.round((masteredQuestions / totalQuestions) * 100) : 0;
    
    // 计算平均掌握等级
    const avgMasteryLevel = totalQuestions > 0 ? 
        (relatedQuestions.map(p => p.mastery_level || 0).reduce((a, b) => a + b, 0) / totalQuestions).toFixed(1) : 0;
    
    // 生成进度条
    const progressBarLength = 20;
    const filledBars = Math.round((masteryPercentage / 100) * progressBarLength);
    const emptyBars = progressBarLength - filledBars;
    const progressBar = "█".repeat(filledBars) + "░".repeat(emptyBars);
    
    // 显示掌握度概览
    dv.header(3, "🎯 掌握度概览");
    dv.paragraph(`
**总题数：** ${totalQuestions} 道
**掌握度：** ${masteryPercentage}% ${progressBar}
**平均等级：** ${avgMasteryLevel}/5.0 ⭐

**状态分布：**
- ✅ 已掌握：${masteredQuestions} 道 (${Math.round(masteredQuestions/totalQuestions*100)}%)
- 🔄 复习中：${reviewingQuestions} 道 (${Math.round(reviewingQuestions/totalQuestions*100)}%)
- 🆕 新题：${newQuestions} 道 (${Math.round(newQuestions/totalQuestions*100)}%)
    `);
}
```

---

**💡 使用说明：** 当你在错题中使用 [[政治/马克思主义基本原理]] 链接时，该知识点的掌握度数据会自动更新。
