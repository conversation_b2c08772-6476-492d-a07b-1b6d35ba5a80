# 🎯 SRS 智能错题管理系统配置指南

## 📋 系统概览

你的错题管理系统已经升级为智能化的间隔重复系统 (SRS)，具备以下核心功能：

### ✨ 主要功能
1. **智能复习调度**：基于遗忘曲线的科学复习间隔
2. **掌握度可视化**：知识点掌握情况一目了然
3. **自动化统计**：错误原因分析和学习进度追踪
4. **个性化复习**：根据掌握程度调整复习频率

## 🚀 快速开始

### 1. 创建新错题
使用模板 `05_Templates/T_WrongQuestion.md` 创建新的错题笔记。新模板包含：
- **SRS 字段**：`status`, `next_review_date`, `mastery_level`, `review_history`
- **复习区域**：显示当前状态和复习历史
- **智能提醒**：自动计算下次复习时间

### 2. 创建知识点笔记
使用模板 `05_Templates/T_KnowledgePoint.md` 创建知识点笔记，自动显示：
- **掌握度统计**：总题数、掌握率、平均等级
- **进度条可视化**：直观的掌握度进度条
- **智能建议**：基于数据的复习建议

### 3. 使用复习主页
打开 `错题本主页.md` 查看：
- **今日复习队列**：需要复习的错题列表
- **学科统计**：各学科掌握情况
- **错误原因分析**：最常见的错误类型

## ⚙️ 配置 QuickAdd 插件（可选）

为了获得最佳体验，建议配置 QuickAdd 插件来实现一键复习功能：

### 步骤 1：安装插件
1. 打开 Obsidian 设置 → 社区插件
2. 搜索并安装 "QuickAdd" 插件
3. 启用插件

### 步骤 2：配置复习脚本
1. 在 QuickAdd 设置中点击 "Add Choice"
2. 选择 "Macro" 类型
3. 命名为 "SRS Review"
4. 添加脚本：选择 `05_Templates/QuickAdd_SRS_Review.js`
5. 设置快捷键（建议：Ctrl+R）

### 步骤 3：使用复习功能
1. 打开任意错题文件
2. 按快捷键或使用命令面板搜索 "SRS Review"
3. 选择复习结果：简单/模糊/忘记
4. 系统自动更新复习数据

## 📊 系统字段说明

### SRS 核心字段
```yaml
status: 新题 | 复习中 | 已掌握
next_review_date: "2025-07-30"  # 下次复习日期
mastery_level: 0-5              # 掌握等级 (0=完全不会, 5=完全掌握)
review_history: []              # 复习历史记录
last_reviewed: "2025-07-29"     # 上次复习日期
```

### 复习间隔算法
- **等级 0**：1 天后复习
- **等级 1**：3 天后复习  
- **等级 2**：7 天后复习
- **等级 3**：15 天后复习
- **等级 4**：30 天后复习
- **等级 5**：90 天后复习（已掌握）

### 复习结果处理
- **简单 ✅**：掌握等级 +1，按新等级设置间隔
- **模糊 🤔**：等级不变，间隔稍微延长（60%）
- **忘记 ❌**：掌握等级 -1，明天重新复习

## 🎯 使用工作流

### 日常复习流程
1. **打开主页**：查看今日复习任务
2. **逐个复习**：点击错题链接进行复习
3. **更新状态**：使用 QuickAdd 脚本或手动更新
4. **查看进度**：在知识点笔记中查看掌握度

### 添加新错题流程
1. **使用模板**：从 `T_WrongQuestion.md` 创建新错题
2. **填写内容**：题目、答案、错误分析
3. **链接知识点**：使用 `[[知识点名称]]` 建立关联
4. **设置标签**：选择适当的错误原因标签

### 知识点管理流程
1. **创建知识点**：使用 `T_KnowledgePoint.md` 模板
2. **查看统计**：自动显示相关错题的掌握情况
3. **分析薄弱点**：根据掌握度数据调整学习重点
4. **跟踪进度**：定期查看掌握度变化

## 🔧 高级配置

### 自定义复习间隔
如需调整复习间隔，修改 `QuickAdd_SRS_Review.js` 中的 `intervals` 数组：
```javascript
const intervals = [1, 3, 7, 15, 30, 90]; // 可自定义天数
```

### 自定义掌握度阈值
在知识点模板中调整掌握度评级标准：
```javascript
if (masteryPercentage < 30) {
    // 调整阈值：30% → 你的期望值
}
```

### 添加新的统计维度
在主页脚本中添加新的分析维度，如：
- 按难度统计
- 按错误次数统计
- 按复习频率统计

## 🎉 系统优势

### 相比传统方法的改进
1. **科学性**：基于遗忘曲线的复习间隔
2. **自动化**：减少手动管理工作
3. **可视化**：直观的进度展示
4. **个性化**：根据个人掌握情况调整
5. **数据驱动**：基于统计数据的学习建议

### 预期效果
- **提高效率**：专注于薄弱知识点
- **减少遗忘**：科学的复习时机
- **增强动机**：可视化的进步反馈
- **优化时间**：避免过度复习已掌握内容

## 🆘 常见问题

### Q: 如何批量更新旧错题？
A: 可以手动为旧错题添加 SRS 字段，或使用 Obsidian 的批量编辑功能。

### Q: 复习历史显示为空？
A: 确保 `review_history` 字段格式正确，应为数组格式。

### Q: 知识点掌握度不更新？
A: 检查错题中是否正确使用了 `[[知识点名称]]` 链接格式。

### Q: 如何备份复习数据？
A: SRS 数据存储在文件的 frontmatter 中，随 Obsidian 库一起备份即可。

---

🎯 **开始使用你的智能错题管理系统，让学习更科学、更高效！**
