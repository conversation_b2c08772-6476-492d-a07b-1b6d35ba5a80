# 🎯 智能错题复习系统

## 📊 今日复习概览

```dataviewjs
// 获取今天的日期
const today = moment().format('YYYY-MM-DD');

// 查询所有错题
const wrongQuestions = dv.pages('"02_WrongQuestions"')
    .where(p => p.tags && p.tags.includes("错题"));

// 统计各状态的题目数量
const statusStats = {
    '新题': wrongQuestions.where(p => p.status === '新题').length,
    '复习中': wrongQuestions.where(p => p.status === '复习中').length,
    '已掌握': wrongQuestions.where(p => p.status === '已掌握').length
};

// 今日待复习题目
const todayReview = wrongQuestions
    .where(p => p.next_review_date && p.next_review_date <= today && p.status !== '已掌握')
    .sort(p => p.next_review_date);

// 按学科统计今日复习
const subjectStats = {};
todayReview.forEach(p => {
    const subject = p.subject || '未分类';
    subjectStats[subject] = (subjectStats[subject] || 0) + 1;
});

// 显示统计信息
dv.header(3, "📈 系统统计");
dv.paragraph(`
**总题库状态：**
- 🆕 新题：${statusStats['新题']} 道
- 🔄 复习中：${statusStats['复习中']} 道  
- ✅ 已掌握：${statusStats['已掌握']} 道

**今日复习任务：** ${todayReview.length} 道题目
`);

if (Object.keys(subjectStats).length > 0) {
    dv.paragraph("**按学科分布：**");
    Object.entries(subjectStats).forEach(([subject, count]) => {
        dv.paragraph(`- ${subject}：${count} 道`);
    });
}
```

## 🎯 今日复习队列

```dataviewjs
// 获取今天的日期
const today = moment().format('YYYY-MM-DD');

// 查询今日需要复习的错题
const todayReview = dv.pages('"02_WrongQuestions"')
    .where(p => p.tags && p.tags.includes("错题"))
    .where(p => p.next_review_date && p.next_review_date <= today && p.status !== '已掌握')
    .sort(p => p.next_review_date);

if (todayReview.length === 0) {
    dv.paragraph("🎉 **恭喜！今天没有需要复习的错题！**");
} else {
    dv.header(4, `📝 待复习题目 (${todayReview.length} 道)`);
    
    // 创建表格
    const tableData = todayReview.map(p => {
        const masteryStars = "★".repeat(p.mastery_level || 0) + "☆".repeat(5 - (p.mastery_level || 0));
        const overdueText = p.next_review_date < today ? "⚠️ 逾期" : "";
        
        return [
            `[[${p.file.name}|${p.file.name.replace('.md', '')}]]`,
            p.subject || "未分类",
            p.next_review_date,
            masteryStars,
            overdueText,
📝 点击题目进行复习
        ];
    });
    
    dv.table(
        ["题目", "学科", "计划复习日期", "掌握等级", "状态", "操作"],
        tableData
    );
}
```

## 📚 学科复习统计

```dataviewjs
// 按学科统计错题情况
const wrongQuestions = dv.pages('"02_WrongQuestions"')
    .where(p => p.tags && p.tags.includes("错题"));

const subjectAnalysis = {};

wrongQuestions.forEach(p => {
    const subject = p.subject || '未分类';
    if (!subjectAnalysis[subject]) {
        subjectAnalysis[subject] = {
            total: 0,
            new: 0,
            reviewing: 0,
            mastered: 0,
            avgMastery: 0
        };
    }
    
    subjectAnalysis[subject].total++;
    
    switch(p.status) {
        case '新题':
            subjectAnalysis[subject].new++;
            break;
        case '复习中':
            subjectAnalysis[subject].reviewing++;
            break;
        case '已掌握':
            subjectAnalysis[subject].mastered++;
            break;
    }
    
    subjectAnalysis[subject].avgMastery += (p.mastery_level || 0);
});

// 计算平均掌握度
Object.keys(subjectAnalysis).forEach(subject => {
    const data = subjectAnalysis[subject];
    data.avgMastery = data.total > 0 ? (data.avgMastery / data.total).toFixed(1) : 0;
    data.masteryPercent = data.total > 0 ? ((data.mastered / data.total) * 100).toFixed(1) : 0;
});

if (Object.keys(subjectAnalysis).length > 0) {
    const tableData = Object.entries(subjectAnalysis).map(([subject, data]) => [
        subject,
        data.total,
        data.new,
        data.reviewing,
        data.mastered,
        `${data.masteryPercent}%`,
        data.avgMastery
    ]);
    
    dv.table(
        ["学科", "总题数", "新题", "复习中", "已掌握", "掌握率", "平均等级"],
        tableData
    );
}
```

## 🔥 错误原因分析

```dataviewjs
// 统计错误原因标签
const wrongQuestions = dv.pages('"02_WrongQuestions"')
    .where(p => p.tags && p.tags.includes("错题"));

const reasonStats = {};

wrongQuestions.forEach(p => {
    if (p.tags) {
        p.tags.forEach(tag => {
            if (tag.startsWith('原因/')) {
                const reason = tag.replace('原因/', '');
                reasonStats[reason] = (reasonStats[reason] || 0) + 1;
            }
        });
    }
});

if (Object.keys(reasonStats).length > 0) {
    dv.header(4, "📊 最常见错误原因");
    
    const sortedReasons = Object.entries(reasonStats)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10);
    
    const tableData = sortedReasons.map(([reason, count]) => [
        reason,
        count,
        `${"█".repeat(Math.ceil(count / Math.max(...Object.values(reasonStats)) * 20))}`
    ]);
    
    dv.table(
        ["错误原因", "出现次数", "频率图"],
        tableData
    );
}
```

---

## ⚙️ 系统说明

### 📖 复习状态说明
- **新题**：刚创建的错题，明天开始复习
- **复习中**：正在间隔复习过程中的题目
- **已掌握**：掌握等级达到5级的题目

### 🎯 掌握等级说明
- ⭐☆☆☆☆ (1级)：刚开始学习
- ⭐⭐☆☆☆ (2级)：有印象但不熟练
- ⭐⭐⭐☆☆ (3级)：基本掌握
- ⭐⭐⭐⭐☆ (4级)：熟练掌握
- ⭐⭐⭐⭐⭐ (5级)：完全掌握

### 🔄 复习间隔算法
- **简单**：掌握等级+1，复习间隔延长（3天→7天→15天→30天→90天）
- **模糊**：掌握等级不变，复习间隔稍微延长（1天→2天→3天）
- **忘记**：掌握等级-1，明天重新复习

