# 📚 知识库导航

## 🎯 按学科浏览知识点

### 📊 高等数学
```dataview
LIST
FROM "03_KnowledgeBase/高等数学"
SORT file.name ASC
```

### 🔢 线性代数  
```dataview
LIST
FROM "03_KnowledgeBase/线性代数"
SORT file.name ASC
```

### 🏛️ 政治
```dataview
LIST
FROM "03_KnowledgeBase/政治"
SORT file.name ASC
```

### 🌍 英语
```dataview
LIST
FROM "03_KnowledgeBase/英语"
SORT file.name ASC
```

## 📊 知识点掌握度统计

```dataviewjs
// 按学科统计知识点掌握情况
const subjects = ['高等数学', '线性代数', '概率论与数理统计', '政治', '英语'];
const subjectStats = {};

// 初始化统计对象
subjects.forEach(subject => {
    subjectStats[subject] = {
        totalKnowledgePoints: 0,
        totalQuestions: 0,
        masteredQuestions: 0,
        avgMastery: 0
    };
});

// 统计每个学科的知识点
subjects.forEach(subject => {
    const knowledgePoints = dv.pages(`"03_KnowledgeBase/${subject}"`);
    subjectStats[subject].totalKnowledgePoints = knowledgePoints.length;
    
    // 统计每个知识点相关的错题
    knowledgePoints.forEach(kp => {
        const relatedQuestions = dv.pages('"02_WrongQuestions"')
            .where(p => p.tags && p.tags.includes("错题"))
            .where(p => p.file.outlinks.some(link => link.path.includes(kp.file.name)));
        
        subjectStats[subject].totalQuestions += relatedQuestions.length;
        subjectStats[subject].masteredQuestions += relatedQuestions.where(p => p.status === '已掌握').length;
    });
    
    // 计算平均掌握度
    if (subjectStats[subject].totalQuestions > 0) {
        subjectStats[subject].avgMastery = Math.round(
            (subjectStats[subject].masteredQuestions / subjectStats[subject].totalQuestions) * 100
        );
    }
});

// 显示统计表格
dv.header(3, "📈 各学科掌握度概览");

const tableData = subjects.map(subject => {
    const stats = subjectStats[subject];
    const progressBar = "█".repeat(Math.ceil(stats.avgMastery / 5)) + "░".repeat(20 - Math.ceil(stats.avgMastery / 5));
    
    return [
        subject,
        stats.totalKnowledgePoints,
        stats.totalQuestions,
        stats.masteredQuestions,
        `${stats.avgMastery}%`,
        progressBar
    ];
});

dv.table(
    ["学科", "知识点数", "相关错题", "已掌握", "掌握率", "进度条"],
    tableData
);
```

## 🔍 快速搜索

### 按知识点名称搜索
```dataview
TABLE file.folder as "学科", file.ctime as "创建时间"
FROM "03_KnowledgeBase"
WHERE file.name != "知识库导航"
SORT file.name ASC
```

### 最近更新的知识点
```dataview
TABLE file.folder as "学科", file.mtime as "更新时间"
FROM "03_KnowledgeBase"
WHERE file.name != "知识库导航"
SORT file.mtime DESC
LIMIT 10
```

## 🎯 学习建议

```dataviewjs
// 分析学习情况并给出建议
const subjects = ['高等数学', '线性代数', '政治', '英语'];
const suggestions = [];

subjects.forEach(subject => {
    const knowledgePoints = dv.pages(`"03_KnowledgeBase/${subject}"`);
    let totalQuestions = 0;
    let masteredQuestions = 0;
    
    knowledgePoints.forEach(kp => {
        const relatedQuestions = dv.pages('"02_WrongQuestions"')
            .where(p => p.tags && p.tags.includes("错题"))
            .where(p => p.file.outlinks.some(link => link.path.includes(kp.file.name)));
        
        totalQuestions += relatedQuestions.length;
        masteredQuestions += relatedQuestions.where(p => p.status === '已掌握').length;
    });
    
    if (totalQuestions > 0) {
        const masteryRate = (masteredQuestions / totalQuestions) * 100;
        
        if (masteryRate < 30) {
            suggestions.push(`🔥 **${subject}**：掌握度较低(${masteryRate.toFixed(1)}%)，建议重点关注基础概念`);
        } else if (masteryRate < 70) {
            suggestions.push(`📈 **${subject}**：掌握度中等(${masteryRate.toFixed(1)}%)，继续巩固练习`);
        } else {
            suggestions.push(`🎉 **${subject}**：掌握度良好(${masteryRate.toFixed(1)}%)，保持复习节奏`);
        }
    } else if (knowledgePoints.length > 0) {
        suggestions.push(`📝 **${subject}**：已创建${knowledgePoints.length}个知识点，但暂无相关错题`);
    }
});

if (suggestions.length > 0) {
    dv.header(3, "💡 个性化学习建议");
    suggestions.forEach(suggestion => {
        dv.paragraph(suggestion);
    });
}
```

## 📋 知识点管理工具

### 🆕 创建新知识点
1. 选择对应学科文件夹
2. 使用 `05_Templates/T_KnowledgePoint.md` 模板
3. 按照学科命名规范：`学科/具体知识点`

### 🔗 链接规范
在错题中引用知识点时使用完整路径：
- `[[高等数学/函数单调性]]`
- `[[线性代数/矩阵的秩]]`
- `[[政治/马克思主义基本原理]]`
- `[[英语/阅读理解技巧]]`

### 🏷️ 标签规范
配合使用对应的标签：
- `知识点/函数单调性`
- `知识点/矩阵的秩`
- `知识点/马克思主义基本原理`
- `知识点/阅读理解技巧`

---

**💡 使用提示**：
1. 定期查看此导航页了解各学科知识点覆盖情况
2. 根据掌握度统计调整学习重点
3. 保持知识点命名的一致性和规范性
4. 及时为新遇到的知识点创建对应的笔记
